//============================================================================
// Name        : KKO_V4.cpp
// Author      : MTS Inc.
// Version     : V 4.0.0
// Copyright   : Copyright@MTS
// Description : Remove multimap for scheduling and make array for thats
//============================================================================
/* include */
#include <iostream>
#include <map>
#include <signal.h>
#include <errno.h>
#include <pthread.h>
#include <vector>
#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <string.h>
#include <unistd.h>
#include <arpa/inet.h>

#include <sys/socket.h>
#include <sys/stat.h>
#include <sys/un.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <sys/timeb.h>
#include <sys/types.h>

#include <code_info.h>
#include <message_info.h>
#include <ml_ctrlsub.h>

#include "Properties.h"
#include "myException.h"
#include "Curl.h"
#include "json.h"
#include "alimTalkApi.h"
#include "DatabaseORA_MMS.h"

using namespace std;

/* definition*/
#define MAX_THREAD_POOL 128


#define MAX_MUNBER_OF_KAKAO_SERAIL 30

/* Declare structure and variables */
// Thread
typedef struct _basicInfo
{
	pthread_t tid;
	int inum;
} basicInfo;

struct _message_info message_info;
struct _shm_info *shm_info;

// condition variable for each threads
pthread_cond_t *mycond;
// condition variabel to synchronize each threads
pthread_cond_t async_cond = PTHREAD_COND_INITIALIZER;

// mutext for critical section of each threads
pthread_mutex_t mutex_lock = PTHREAD_MUTEX_INITIALIZER;
// mutex for approaching database
pthread_mutex_t mutex_db_lock = PTHREAD_MUTEX_INITIALIZER;
// mute to synchronize each thread
pthread_mutex_t async_mutex = PTHREAD_MUTEX_INITIALIZER;
// mutex for scheduling
//pthread_mutex_t schedule_lock = PTHREAD_MUTEX_INITIALIZER;

// Mgmt process
int activeProcess = true;
char PROCESS_NO[7], PROCESS_NAME[36];

// Database
KSKYB::CDatabaseORA g_oracle;
sql_context ctx;

// scheduling
bool *setFlag;
vector< map<string, string> > sendMsgVector;

// Configuration
KSKYB::CProperties g_prop;
char quid[32];
char qname[32];
char target_url[128];
int nThreadCnt = 0;
/* check time */
//time_t start, end;
struct timeval start, end_time;
long seconds, useconds;
double mtime;

/* define functions */
void *procSendRept(void *param);
void Init_Server();
void CloseProcess(int sig);
void mnt(char *buf, int st, int err);
void log(char *buf, int st, int err);
void makeCurrentTime(string &cTime);
//void Eliminate(char *str, char *ch);
int CheckThreadStatus(basicInfo **param, int nCnt);

/* implementation */
int main(int argc, char *argv[])
{
	char logMsg[1024];
	pthread_attr_t attr;
	void * status;
	// time
	time_t now, last;
	double diff;

	// Initialize and set thread joinable
	pthread_attr_init(&attr);
	pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_JOINABLE);

	/* configuration */
	memset(quid, 0x00, sizeof(quid));
	memset(qname, 0x00, sizeof(qname));
	memset(target_url, 0x00, sizeof(target_url));

	g_prop.load(argv[1]);
	nThreadCnt = g_prop.getPropertyInt("gw.tcnt");
	sprintf(quid, "%s", g_prop.getProperty("gw.quid"));
	sprintf(qname, "%s", g_prop.getProperty("gw.qname"));
	sprintf(target_url, "%s", g_prop.getProperty("gw.target_url"));

	
	// Build interface with command processes
	Init_Server();
	if (ml_sub_init(PROCESS_NO, PROCESS_NAME, (char *)0, (char *)0) < 0) {
		sprintf(logMsg, "[%s())][ERR][ml_sub_init ERROR.]", __func__);
		mnt(logMsg, 0, 0);
		return 0;
	}
	
	sprintf(logMsg, "[%s():%d][DEB][get configuration file]", __func__, __LINE__);
		log(logMsg, 0, 0);
	
	
	// Oracle tasks
	if (g_oracle.setEnableThreads() < 0) {
		sprintf(logMsg, "[%s():%d][ERR][setEnableThreads ERROR. process return;]", __func__, __LINE__);
		mnt(logMsg, 0, 0);
		return 0;
	}

	if (g_oracle.initThread(ctx) < 0 || ctx == NULL) {
		sprintf(logMsg, "[%s():%d][ERR][g_oracle.initThread Fail.]", __func__, __LINE__);
		mnt(logMsg, 0, 0);
		return -1;
	}

	if (g_oracle.connectToOracle(ctx, g_prop.getProperty("db.uid"), g_prop.getProperty("db.dsn")) < 0) {
		sprintf(logMsg, "[%s():%d][ERR][g_oracle.connectToOracle() Fail..]", __func__, __LINE__);
		mnt(logMsg, 0, 0);
		return -1;
	}

	// initialize curl
	curl_global_init(CURL_GLOBAL_DEFAULT);

	// initialize thread
	// create variables about the number of thread
	mycond = (pthread_cond_t *)malloc(sizeof(pthread_cond_t) * nThreadCnt);
	basicInfo tBasicInfo[nThreadCnt];
	memset((void *)&tBasicInfo, 0x00, sizeof(tBasicInfo));

	// scheduling
	setFlag = new bool[nThreadCnt];
	sendMsgVector.resize(nThreadCnt);

	// Craete thread pool
	for (int i = 0; nThreadCnt > i; ++i) {
		tBasicInfo[i].inum = i;

		pthread_cond_init(&mycond[i], NULL);

		pthread_mutex_lock(&async_mutex);
		pthread_create(&tBasicInfo[i].tid, &attr, procSendRept, &tBasicInfo[i]);
		//pthread_create(&tBasicInfo[i].tid, NULL, procSendRept, &tBasicInfo[i]);
		pthread_cond_wait(&async_cond, &async_mutex);
		pthread_mutex_unlock(&async_mutex);
	}

	// record current time
	time(&last);
	map<string, string> mapSendMsg;
	long long msgid = 0;
	//int tmpK = 0;
	int k = 0; // scheduling
	while (activeProcess) {
		//usleep(50000);
		usleep(10000);
		// get a message
		gettimeofday(&start, NULL);

		pthread_mutex_lock(&mutex_db_lock);
		msgid = g_oracle.getMsgData_V4(ctx, qname, mapSendMsg);
		pthread_mutex_unlock(&mutex_db_lock);

		gettimeofday(&end_time, NULL);

		seconds  = end_time.tv_sec  - start.tv_sec;
		useconds = end_time.tv_usec - start.tv_usec;

		mtime = ((seconds) + ((double)useconds / 1000000));

		//sprintf(logMsg, "[%s()][DEB] check elapsed time(dequeue): %2.3f]", __func__, mtime);
		//mnt(logMsg, 0, 0);


		if (msgid < 0) {
		   /* DB fail */
			activeProcess = false;
			sprintf(logMsg, "[%s()][ERR][msgid is -1(ERROR) activeProcess = false]", __func__);
			mnt(logMsg, 0, 0);
			continue;
		} else if (0 < msgid) {
			//sprintf(logMsg, "[%s()][DEB][get msg from db: %lld]", __func__, msgid);
			//log(logMsg, 0, 0);

			// scheduling
			while (true) {
				if (k == nThreadCnt) {
					// no thread available
					sprintf(logMsg, "[%s()][INF][no thread available]", __func__);
					log(logMsg, 0, 0);
					usleep(10000); // sleep 0.01s
					k = 0;
					continue;
				} else {
					if (setFlag[k] == false) {
						// The 'k'th subthread takes a rest now
						// prepare for task to subthread
						sendMsgVector.at(k) = mapSendMsg;
						// send signal
						pthread_cond_signal(&mycond[k]);
#ifdef DEBUG
						sprintf(logMsg, "[%s()][INF][signal: %d]", __func__, k);
						log(logMsg, 0, 0);
#endif
						break;
					}
					++k;
				} // if (k)
			} // while (true)
		} else {
			/* unknown error */
		} //if (msgid)

		//check threads alive
		time(&now);
		diff = difftime(now, last);
		if (diff > 120) {
			CheckThreadStatus((basicInfo **)tBasicInfo, nThreadCnt);

			time(&last);
			//usleep(100000);
		}
		mapSendMsg.clear();
		msgid = 0;
	} //while (activeProcess)

	/* End of the process */
	free(mycond);
	// threads termination
	pthread_attr_destroy(&attr);
	for (int k = 0; nThreadCnt > k; ++k) {
		int rc = pthread_join(tBasicInfo[k].inum, &status);
		if (rc) {
			// join error
			sprintf(logMsg, "[%s()][ERR][Unable to join, %d]", __func__, rc);
			mnt(logMsg, 0, 0);
			//exit(-1);
		}
		// sucess, status
		sprintf(logMsg, "[%s()][INF][Completed thread id: %d]", __func__, k);
		log(logMsg, 0, 0);
	}
	pthread_exit(NULL);

	// release curl resources
	curl_global_cleanup();
	// release process management program
	ml_sub_end();
	// close oracle session
	g_oracle.closeFromOracle(ctx);

	sprintf(logMsg, "[%s()][INF][main Process End.]", __func__);
	mnt(logMsg, 0, 0);

	return 0;
}

int CheckThreadStatus(basicInfo **param, int nThreadCnt)
{
	char logMsg[256];
	int idx, status;
	basicInfo tpSub[nThreadCnt];

	memset((void *)&tpSub, 0x00, sizeof(tpSub));
	memcpy(tpSub, param, sizeof(basicInfo) * nThreadCnt);

	sprintf(logMsg, "[%s()][thread check]", __func__);
	mnt(logMsg, 0, 0);

	for (idx = 0; idx < nThreadCnt; idx++) {
		if (pthread_kill(tpSub[idx].tid, 0) != 0) {
			//pthread_join(tpSub[idx].tid, (void **)&status);
			activeProcess = false;
			sprintf(logMsg, "[%s()][CheckThreadStatus fail]", __func__);
			mnt(logMsg, 0, 0);
			return -1;
		}
	}

	return 0;
}

void *procSendRept(void *param) {
	int res = -1;
	/* subthread */
	basicInfo bi; // passed thread arguments
	memset((void *)&bi, 0x00, sizeof(bi));
	memcpy(&bi, param, sizeof(bi));

	// thread number
	int mynum = bi.inum;

	// logging
	char logMsg[1024];
	memset(logMsg, 0x00, sizeof(logMsg));

	pthread_mutex_lock(&async_mutex);
	pthread_cond_signal(&async_cond);
	pthread_mutex_unlock(&async_mutex);

	sprintf(logMsg, "[%s()][INF][Thread Start tid: %d]", __func__, mynum);
	log(logMsg, 0, 0);

	// scheduling
	setFlag[mynum] = true;

	// initialize curl library
	CCurl curl;
	CURLcode rVal;

	try {
		while (activeProcess) {
			// scheduling
			pthread_mutex_lock(&mutex_lock);
#ifdef DEBUG
			snprintf(logMsg, sizeof(logMsg), "[%s()][TID: %d][DEB] Before scheduling.", __func__, mynum);
			log(logMsg, 0, 0);
#endif
			setFlag[mynum] = false;
			pthread_cond_wait(&mycond[mynum], &mutex_lock);
#ifdef DEBUG
			snprintf(logMsg, sizeof(logMsg), "[%s()][TID: %d][DEB] After scheduling.", __func__, mynum);
			log(logMsg, 0, 0);
#endif
			setFlag[mynum] = true;
			pthread_mutex_unlock(&mutex_lock);

			/* check time */
			//start = time(NULL);
			gettimeofday(&start, NULL);

			// get message to transmit
			map<string, string> mapSendMsg;
			mapSendMsg = sendMsgVector.at(mynum);

			/* send message to kakao*/
#ifdef DEBUG
			/*snprintf(logMsg, sizeof(logMsg), "[%s()][TID: %d][DEB]prepare data for kakao. charset: %s", __func__, mynum, mapSendMsg["char_set"].c_str());
			log(logMsg, 0, 0);*/
#endif
			CAlimtalkApi ata;
			string parameter = "";
			long long mms_id = atoll(mapSendMsg["mms_id"].c_str());
			ata.makeSmsRequestMsg_V4(mapSendMsg, parameter, mms_id);

			map<string, string> mapReport;
			//20180829 msg[%s] -> msg[] privacy infomation eraser
			snprintf(logMsg, sizeof(logMsg)-1, "tnum[%d]send[%lld]msg[]", mynum, mms_id);
			log(logMsg, 0, 0);
			
			snprintf(logMsg, sizeof(logMsg)-1, "tnum[%d]button[%.100s]\n", mynum,mapSendMsg["button"].c_str());
			log(logMsg, 0, 0);
			
			/* Normal execution; send message to kakao */
			curl.init();
			curl.setHeaderPost("Accept: application/json");
			curl.setHeaderPost("Content-type: application/json");
			curl.setOptPost(target_url, parameter);
			curl.response.clear();
			rVal = curl.perform();
			
			sprintf(logMsg, "tnum[%d]mms_id[%lld]rVal[%d]", bi.inum, mms_id, rVal);
			log(logMsg, 0, 0);
	
			if (rVal == CURLE_OK) {
				// success
				snprintf(logMsg, sizeof(logMsg), "tnum[%d]mms_id[%lld]res[%s]", mynum, mms_id, curl.response.c_str());
				log(logMsg, 0, 0);
	
				//parse response data(JSON)
				ST_TALK_RES res;
				int ret = ata.parsingResponse(curl.response.c_str(), res);
				if (ret < 0) {
					/* JSON parsing error */
					sprintf(logMsg, "tnum[%d] mms_id[%lld] response parsing error", mynum, mms_id);
					log(logMsg, 0, 0);
					
					char cmsg_id[32] = {
						0x00,
					};
					sprintf(cmsg_id, "%lld", mms_id);
					mapReport["msg_id"] = cmsg_id;
	
					string cTime;
					makeCurrentTime(cTime);
	
					mapReport["dlv_date"] = cTime;
					mapReport["res_code"] = "8001";
					mapReport["res_text"] = "fail";
					mapReport["end_telco"] = "KKO";
					mapReport["rcv_numb"] = mapSendMsg["dst_addr"];
				} else {
					/* success */
					char cmsg_id[32] = {
						0x00,
					};
					sprintf(cmsg_id, "%lld", mms_id);
					mapReport["msg_id"] = cmsg_id;
	
					if (res.received_at.size() > 0) {
						string report_dt;
						ata.makeDateString(res.received_at, report_dt);
						mapReport["dlv_date"] = report_dt;
					} else {
						string cTime;
						makeCurrentTime(cTime);
						mapReport["dlv_date"] = cTime;
					}
					if (res.code == "0000") {
						/* polling */
						if (mapSendMsg["res_method"].compare("polling") == 0) {
							// check res_method
							// save a sender key in array or list. List is more adequate.
							//sprintf(logMsg, "[%s()][TID: %d][DEB][polling. MMSID: %lld]", __func__, mynum, mms_id);
							//log(logMsg, 0, 0);
							goto POLLING_METHOD;
						}

						mapReport["res_code"] = "1000";
						mapReport["res_text"] = "success";
					} else {
						mapReport["res_code"] = res.code;
						mapReport["res_text"] = res.message;

						
					}
	
					mapReport["end_telco"] = "KKO";
					mapReport["rcv_numb"] = mapSendMsg["dst_addr"];
				} // if(JSON parsing)
			}
			else {
				// fail to get curl response
				sprintf(logMsg, "no response msgid[%lld]", mms_id);
				log(logMsg, 0, 0);
	
				char cmsg_id[32] = {
					0x00,
				};
				sprintf(cmsg_id, "%lld", mms_id);
				mapReport["msg_id"] = cmsg_id;
	
				string cTime;
				makeCurrentTime(cTime);
	
				mapReport["dlv_date"] = cTime;
				mapReport["res_code"] = "8002";
				mapReport["res_text"] = "fail";
				mapReport["end_telco"] = "KKO";
				mapReport["rcv_numb"] = mapSendMsg["dst_addr"];
			} // if(curl)

			pthread_mutex_lock(&mutex_db_lock);
			if (g_oracle.setReportData((int)atoi(quid), ctx, mapReport) < 0) {
				pthread_mutex_unlock(&mutex_db_lock);

				sprintf(logMsg, "[%s()][TID: %d][ERR][setReportData error. MMSID: %lld]", __func__, mynum, mms_id);
				mnt(logMsg, 0, 0);
				return NULL;
			}
			pthread_mutex_unlock(&mutex_db_lock);
		 

#ifdef DEBUG
		/* check time */
		//end = time(NULL);
		gettimeofday(&end_time, NULL);

		seconds  = end_time.tv_sec  - start.tv_sec;
		useconds = end_time.tv_usec - start.tv_usec;

		mtime = ((seconds) + ((double)useconds / 1000000));

		//sprintf(logMsg, "[%s()][TID: %d][DEB] check elapsed time: %2.3f]", __func__, mynum, mtime);
		//mnt(logMsg, 0, 0);


		snprintf(logMsg, sizeof(logMsg), "[%s()][TID: %d][DEB] End of the transmit. ptn_sn: %s. mms_id: %s, res_code: %s",
				__func__, mynum, mapReport["ptn_sn"].c_str(),	mapReport["msg_id"].c_str(), mapReport["res_code"].c_str() );
		log(logMsg, 0, 0);
#endif

POLLING_METHOD: // -> continue
			//mapSendMsg.clear();
			//mapReport.clear();
			curl.cleanAll();
		} // while(activeProcess)
	} catch (myException *excp) {
		sprintf(logMsg, "[%s():%d][ERR][catch (myException* excp) procSendRept return -1;]", __func__, __LINE__);
		mnt(logMsg, 0, 0);
		delete excp;
	} catch (...) {
		sprintf(logMsg, "[%s():%d][ERR][catch procSendRept return -1;]", __func__, __LINE__);
		mnt(logMsg, 0, 0);
	}

	// termination	
	pthread_exit(NULL);

	sprintf(logMsg, "procSendRept Thread[%d] End", mynum);
	mnt(logMsg, 0, 0);

	return NULL;
}

void Init_Server() {
	setpgrp();
	printf("Process Running.. Please wait 2 seconds.\n");
	sleep(2);
	signal(SIGHUP, CloseProcess);
	signal(SIGCLD, SIG_IGN);
	signal(SIGPIPE, SIG_IGN);
	signal(SIGTERM, CloseProcess);
	signal(SIGINT, CloseProcess);
	signal(SIGQUIT, CloseProcess);
	signal(SIGKILL, CloseProcess);
	signal(SIGSTOP, CloseProcess);
	signal(SIGUSR1, CloseProcess);
	//	signal(SIGABRT, CloseProcess);
	signal(SIGSEGV, CloseProcess);
}

void CloseProcess(int sig) {
	activeProcess = false;

	char logMsg[256];
	memset(logMsg, 0x00, sizeof(logMsg));
	sprintf(logMsg, "[%s()][INF][CloseProcess Start & Exit [SIG:%d]]", __func__, sig);
	mnt(logMsg, 0, 0);

	if (11 == sig) {
		// segmntation fault
		exit(0);
	}
}

void makeCurrentTime(string &cTime) {
	time_t tm_time;
	struct tm *st_time;
	char buff[1024] = {
		0x00,
	};
	time(&tm_time);
	st_time = localtime(&tm_time);
	strftime(buff, 1024, "%Y%m%d%H%M%S", st_time);
	cTime = buff;
}

void log(char *buf, int st, int err) {
	char log[1024] = {
		0x00,
	};
	snprintf(log, sizeof(log) - 1, "[%s]%s", PROCESS_NAME, buf);

	if (ml_sub_send_log((char *)log, sizeof(log), 3, st, err) <= 0) {
		printf("%s ml_sub_send_log ERROR. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}
}

void mnt(char *buf, int st, int err) {
	char log[1024] = {
		0x00,
	};
	snprintf(log, sizeof(log) - 1, "[%s]%s", PROCESS_NAME, buf);

	if (ml_sub_send_moni((char *)log, sizeof(log), 3, st, err) <= 0) {
		printf("%s ml_sub_send_moni ERROR. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}
}

